<?php

/**
 * Copyright © Commercial Avenue. All rights reserved.
 * See COPYING.txt for license details.
 */

declare(strict_types=1);

namespace Comave\SellerPayouts\Helper;

use Magento\Store\Model\ScopeInterface;
use Psr\Log\LoggerInterface;
use Stripe\Account;
use Stripe\Exception\ApiErrorException;
use Stripe\Stripe;

class Data extends ConfigProvider
{
    /**
     * Get Decrypted Key function
     *
     * @return string
     */
    public function getDecryptedKey(): string
    {
        $key = $this->getStripeKeys();

        return $this->encryptor->decrypt($key['secret_key']);
    }

    /**
     * Check Stripe Account function
     *
     * @param int $customerId
     * @return bool
     */
    public function checkStripeAccount(int $customerId): bool
    {
        try {
            $customer = $this->customerRepository->getById($customerId);
            $stripeAccountId = $customer->getCustomAttribute('stripe_client_id');

            return $stripeAccountId && $stripeAccountId->getValue();
        } catch (\Magento\Framework\Exception\NoSuchEntityException $e) {
            $this->logger->error('Check Stripe Account function NoSuchEntityException: ' . $e->getMessage());

            return false;
        } catch (\Exception $e) {
            $this->logger->error('Check Stripe Account function Exception: ' . $e->getMessage());

            return false;
        }
    }

    /**
     * Create Seller Stripe Account function
     *
     * @param string $email
     * @param string $country
     * @return \Stripe\Account|bool
     */
    public function createSellerStripeAccount(string $email, string $country): \Stripe\Account|bool
    {
        // Force log to system.log to ensure we see it
        error_log("DEBUG: Starting createSellerStripeAccount with email: $email, country: $country");

        // Validate input parameters first
        if (empty($email)) {
            $this->logger->error('Stripe account creation failed: Empty email provided');
            error_log("ERROR: Empty email provided to createSellerStripeAccount");
            return false;
        }

        if (empty($country)) {
            $this->logger->error('Stripe account creation failed: Empty country provided');
            error_log("ERROR: Empty country provided to createSellerStripeAccount");
            return false;
        }

        // Validate email format
        if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
            $this->logger->error('Stripe account creation failed: Invalid email format', ['email' => $email]);
            error_log("ERROR: Invalid email format: $email");
            return false;
        }

        // Validate country code (should be 2-letter ISO code)
        if (strlen($country) !== 2) {
            $this->logger->error('Stripe account creation failed: Invalid country code format', ['country' => $country]);
            error_log("ERROR: Invalid country code format: $country (should be 2-letter ISO code)");
            return false;
        }

        try {
            $stripeKeys = $this->getDecryptedKey();
            error_log("DEBUG: Successfully retrieved Stripe keys, length: " . strlen($stripeKeys ?? ''));
        } catch (\Exception $e) {
            $this->logger->error('Failed to get Stripe keys', ['error' => $e->getMessage(), 'trace' => $e->getTraceAsString()]);
            error_log("ERROR: Failed to get Stripe keys: " . $e->getMessage());
            error_log("ERROR: Stack trace: " . $e->getTraceAsString());
            return false;
        }

        if (empty($stripeKeys)) {
            $this->logger->error('Stripe account creation failed: Empty Stripe API key');
            error_log("ERROR: Empty Stripe API key retrieved");
            return false;
        }

        // Log the attempt with parameters
        $this->logger->info('Attempting to create Stripe account', [
            'email' => $email,
            'country' => $country,
            'stripe_key_configured' => !empty($stripeKeys),
            'stripe_key_length' => strlen($stripeKeys ?? '')
        ]);

        error_log("DEBUG: Stripe key configured: " . (!empty($stripeKeys) ? 'YES' : 'NO'));

        if (empty($stripeKeys)) {
            $this->logger->error('Stripe API key is not configured');
            error_log("ERROR: Stripe API key is not configured");
            return false;
        }

        Stripe::setApiKey($stripeKeys);
        error_log("DEBUG: Stripe API key set, about to create account");

        // Log Stripe configuration details
        $stripeMode = $this->scopeConfig->getValue(self::XML_ADMIN_STRIPE_MODE, ScopeInterface::SCOPE_STORE);
        error_log("DEBUG: Stripe mode: " . ($stripeMode ?? 'not set'));
        error_log("DEBUG: Using " . ($stripeMode === 'test' ? 'TEST' : 'LIVE') . " Stripe environment");

        // Test API key validity by making a simple call
        try {
            $balance = \Stripe\Balance::retrieve();
            error_log("DEBUG: Stripe API key validation successful - can retrieve balance");
        } catch (\Exception $balanceException) {
            error_log("WARNING: Stripe API key validation failed: " . $balanceException->getMessage());
            $this->logger->warning('Stripe API key validation failed', ['error' => $balanceException->getMessage()]);
        }

        try {
            error_log("DEBUG: About to call createAccount method with email: $email, country: $country");
            $account = $this->createAccount($email, $country);

            if (!$account) {
                $this->logger->error('Stripe account creation returned null/false');
                error_log("ERROR: createAccount returned null/false");
                return false;
            }

            if (!isset($account->id)) {
                $this->logger->error('Stripe account creation returned object without ID', [
                    'account_class' => get_class($account),
                    'account_properties' => get_object_vars($account)
                ]);
                error_log("ERROR: Account object missing ID property. Class: " . get_class($account));
                return false;
            }

            $this->logger->info('Stripe account created successfully', [
                'account_id' => $account->id,
                'country' => $account->country,
                'email' => $account->email,
                'type' => $account->type,
                'charges_enabled' => $account->charges_enabled ?? 'unknown',
                'payouts_enabled' => $account->payouts_enabled ?? 'unknown',
                'details_submitted' => $account->details_submitted ?? 'unknown'
            ]);
            error_log("SUCCESS: Stripe account created with ID: " . $account->id);
            error_log("SUCCESS: Account details - Country: " . $account->country . ", Email: " . $account->email . ", Type: " . $account->type);
            return $account;
        } catch (ApiErrorException $e) {
            $errorDetails = [
                'error_message' => $e->getMessage(),
                'error_code' => $e->getStripeCode(),
                'error_type' => $e->getError()->type ?? 'unknown',
                'error_param' => $e->getError()->param ?? 'unknown',
                'error_decline_code' => $e->getError()->decline_code ?? 'unknown',
                'email' => $email,
                'country' => $country,
                'http_status' => $e->getHttpStatus(),
                'request_id' => $e->getError()->request_id ?? 'unknown',
                'stripe_version' => $e->getError()->stripe_version ?? 'unknown'
            ];

            $this->logger->error('Create Seller Stripe Account API Error', $errorDetails);

            // Also log to error_log for immediate visibility with more details
            error_log("STRIPE API ERROR: " . $e->getMessage());
            error_log("STRIPE ERROR CODE: " . ($e->getStripeCode() ?? 'none'));
            error_log("STRIPE ERROR TYPE: " . ($e->getError()->type ?? 'unknown'));
            error_log("STRIPE ERROR PARAM: " . ($e->getError()->param ?? 'unknown'));
            error_log("STRIPE DECLINE CODE: " . ($e->getError()->decline_code ?? 'none'));
            error_log("STRIPE HTTP STATUS: " . $e->getHttpStatus());
            error_log("STRIPE REQUEST ID: " . ($e->getError()->request_id ?? 'unknown'));

            // Log the full error object for debugging
            error_log("STRIPE FULL ERROR OBJECT: " . json_encode($e->getError()));

            return false;
        } catch (\Exception $e) {
            $this->logger->error('General error creating Stripe account', [
                'error_message' => $e->getMessage(),
                'error_class' => get_class($e),
                'error_file' => $e->getFile(),
                'error_line' => $e->getLine(),
                'stack_trace' => $e->getTraceAsString()
            ]);
            error_log("GENERAL ERROR: " . $e->getMessage());
            error_log("GENERAL ERROR CLASS: " . get_class($e));
            error_log("GENERAL ERROR FILE: " . $e->getFile() . " LINE: " . $e->getLine());
            error_log("GENERAL ERROR TRACE: " . $e->getTraceAsString());
            return false;
        }
    }

    /**
     * Get Seller Account Status function
     *
     * @param string $accountId
     * @return bool
     */
    public function getSellerAccountStatus(string $accountId): bool
    {
        $stripeKeys = $this->getDecryptedKey();

        try {
            $stripe = new \Stripe\StripeClient($stripeKeys);
            $account = $stripe->accounts->retrieve($accountId, []);

            return $account->payouts_enabled === true;
        } catch (\Stripe\Exception\ApiErrorException $e) {
            $this->logger->error('Stripe API error: ' . $e->getMessage());

            return false;
        } catch (\Exception $e) {
            $this->logger->error('Error retrieving Stripe account: ' . $e->getMessage());

            return false;
        }
    }

    /**
     * Create Stripe Login Link function
     *
     * @param string $accountId
     * @return bool|string
     */
    public function createStripeLoginLink(string $accountId): bool|string
    {
        $stripeKeys = $this->getDecryptedKey();
        $stripe = new \Stripe\StripeClient($stripeKeys);

        try {
            // Create the login link for the specified account ID
            $response = $stripe->accounts->createLoginLink($accountId, []);

            // Check if the 'url' key exists in the response
            return $response['url'] ? (string) $response['url'] : false;
        } catch (\Exception $e) {
            $this->logger->error('Error creating Stripe login link: ' . $e->getMessage());

            return false;
        }
    }

    /**
     * Delete Seller Stripe Account function
     *
     * @param string $accountId
     * @return bool
     */
    public function deleteSellerStripeAccount(string $accountId): bool
    {
        $stripeKeys = $this->getDecryptedKey();
        Stripe::setApiKey($stripeKeys);

        try {
            $account = Account::retrieve($accountId);
            $account->delete();

            return true;
        } catch (ApiErrorException $e) {
            $this->logger->error($e->getMessage());

            return false;
        }
    }

    /**
     * Stripe account verification link
     *
     * @param string $accountId
     * @return mixed|bool|string|null
     * @throws \Magento\Framework\Exception\NoSuchEntityException
     */
    public function createStripeAccountLink(string $accountId): mixed
    {
        $stripeKeys = $this->getDecryptedKey();
        Stripe::setApiKey($stripeKeys);
        $baseUrl = $this->storeManager->getStore()->getBaseUrl();

        try {
            $accountLink = \Stripe\AccountLink::create([
                'account' => $accountId,
                'refresh_url' => $baseUrl . 'seller_payouts/seller/payouts',
                'return_url' => $baseUrl . 'seller_payouts/seller/payouts',
                'type' => 'account_onboarding',
            ]);

            return $accountLink['url'];
        } catch (ApiErrorException $e) {
            // Handle exception
            $this->logger->error($e->getMessage());

            return false;
        }
    }

    /**
     * When admin wants to pay to seller from the admin dashboard - manual payouts
     *
     * @param int|float $amount
     * @param int|string $sellerId
     * @param string $wksellerorderids
     * @return bool
     */
    public function manualPayout(
        int|float $amount,
        int|string $sellerId,
        string $wksellerorderids
    ): bool|\Magento\Framework\Phrase {
        $stripeID = $this->getStripeId($sellerId);

        if (empty($stripeID)) {
            $this->logger->error('Please create a Stripe account on the Comave Stripe platform.');

            return false;
        }

        try {
            $this->processPayout($amount, $stripeID, $wksellerorderids);
        } catch (\Exception $e) {
            $this->logger->error('Stripe API Manual Payout Error: ' . $e->getMessage());

            return false;
        }

        return true;
    }

    /**
     * Convert Price function
     *
     * @param float $price
     * @param string $currencyCodeTo
     * @return float
     */
    public function convertPrice(float $price, string $currencyCodeTo): float
    {
        $rate = $this->currencyFactory->create()
            ->load($this->getStoreBaseCurrency())
            ->getAnyRate(strtoupper($currencyCodeTo));

        return $price * $rate;
    }

    /**
     * Get PlatForm Currency function
     *
     * @return bool|string
     */
    public function getPlatFormCurrency(): bool|string
    {
        // Retrieve the decrypted Stripe API key
        $stripeSecretKey = $this->getDecryptedKey();
        $accountId = $this->getPlatformStripeId();

        try {
            // Initialize the Stripe client with the decrypted key
            $stripe = new \Stripe\StripeClient($stripeSecretKey);

            // Retrieve the main account details
            $account = $stripe->accounts->retrieve($accountId);

            // Check if the account is a Stripe Connect account
            if ($account->type === 'standard' || $account->type === 'express') {
                // Retrieve the main account's default_currency
                return $account->default_currency;
            }

            // Log error if the account is not a Stripe Connect account
            $this->logger->error('The provided account is not a Stripe Connect account');

            return false;
        } catch (\Stripe\Exception\ApiErrorException $e) {
            // Log Stripe API errors
            $this->logger->error('Stripe API error: ' . $e->getMessage());

            return false;
        } catch (\Exception $e) {
            $this->logger->error('Error retrieving Stripe account: ' . $e->getMessage());

            return false;
        }
    }

    /**
     * Initialize Comave Logger function
     *
     * @return \Psr\Log\LoggerInterface
     */
    public function getLogger(): LoggerInterface
    {
        return $this->logger;
    }

    /**
     * Get Stripe Id function
     *
     * @param int|string $sellerId
     * @return string|null
     */
    private function getStripeId(int|string $sellerId): ?string
    {
        $customer = $this->customerModel->load((int) $sellerId);

        return $customer->getStripeClientId();
    }

    /**
     * Process Payout function
     *
     * @param int|float $amount
     * @param string $stripeID
     * @param string $wksellerorderids
     * @return void
     */
    private function processPayout(int|float $amount, string $stripeID, string $wksellerorderids): void
    {
        $currency = $this->getPlatFormCurrency();
        $stripe = new \Stripe\StripeClient($this->getDecryptedKey());
        $stripe->transfers->create([
            'amount' => (int) round($this->convertPrice($amount, $currency) * 100),
            'currency' => $currency,
            'destination' => $stripeID,
            'transfer_group' => 'ORDER_ID_' . $wksellerorderids,
        ]);
    }

    /**
     * Get Stripe Keys function
     *
     * @param int|string|null $store
     * @return mixed[]
     */
    private function getStripeKeys(int|string|null $store = null): array
    {
        $mode = $this->scopeConfig->getValue(self::XML_ADMIN_STRIPE_MODE, ScopeInterface::SCOPE_STORE, $store);

        return [
            'publishable_key' => $this->scopeConfig->getValue(
                $mode === 'test'
                    ? self::XML_ADMIN_STRIPE_TEST_PUBLISHABLE_KEY
                    : self::XML_ADMIN_STRIPE_LIVE_PUBLISHABLE_KEY,
                ScopeInterface::SCOPE_STORE,
                $store
            ),
            'secret_key' => $this->scopeConfig->getValue(
                $mode === 'test'
                    ? self::XML_ADMIN_STRIPE_TEST_SECRET_KEY
                    : self::XML_ADMIN_STRIPE_LIVE_SECRET_KEY,
                ScopeInterface::SCOPE_STORE,
                $store
            )
        ];
    }

    /**
     * Create a Stripe account with the given parameters.
     *
     * @param string $email
     * @param string $country
     * @return \Stripe\Account
     */
    private function createAccount(string $email, string $country): \Stripe\Account
    {
        error_log("DEBUG: createAccount called with email: $email, country: $country");

        // Validate payout days configuration
        $payoutDays = $this->getSellerPayoutsDays();
        error_log("DEBUG: Seller payout days: $payoutDays");

        if ($payoutDays < 1 || $payoutDays > 31) {
            error_log("WARNING: Invalid payout days value: $payoutDays (should be 1-31)");
        }

        $accountData = [
            'capabilities' => [
                'card_payments' => ['requested' => true],
                'transfers' => ['requested' => true],
            ],
            'country' => $country,
            'email' => $email,
            'settings' => [
                'payouts' => [
                    'schedule' => [
                        'interval' => 'monthly',
                        'monthly_anchor' => $payoutDays,
                    ],
                ],
            ],
            'type' => 'express',
        ];

        // Log the exact data being sent to Stripe
        $this->logger->info('Creating Stripe account with data', [
            'account_data' => $accountData,
            'payout_days' => $payoutDays,
            'email_length' => strlen($email),
            'country_length' => strlen($country)
        ]);

        error_log("DEBUG: About to call Stripe Account::create with data: " . json_encode($accountData, JSON_PRETTY_PRINT));
        error_log("DEBUG: Stripe SDK version: " . (defined('Stripe\Stripe::VERSION') ? \Stripe\Stripe::VERSION : 'unknown'));

        try {
            $account = Account::create($accountData);
            error_log("DEBUG: Stripe Account::create successful, returned account ID: " . ($account->id ?? 'no-id'));
            return $account;
        } catch (\Exception $e) {
            error_log("ERROR in createAccount: " . $e->getMessage());
            error_log("ERROR in createAccount class: " . get_class($e));
            throw $e; // Re-throw to be caught by the calling method
        }
    }
}
