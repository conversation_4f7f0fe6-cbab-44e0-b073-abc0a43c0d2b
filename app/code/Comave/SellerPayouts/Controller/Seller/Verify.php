<?php

/**
 * Copyright © Commercial Avenue. All rights reserved.
 * See COPYING.txt for license details.
 */

declare(strict_types=1);

// Verify seller Stripe account details are correct or not

namespace Comave\SellerPayouts\Controller\Seller;

use Comave\SellerPayouts\Helper\Data as StripeHelper;
use Magento\Customer\Model\Customer;
use Magento\Customer\Model\Session as CustomerSession;
use Magento\Framework\App\Action\Action;
use Magento\Framework\App\Action\Context;
use Magento\Framework\Controller\Result\Redirect;
use Magento\Framework\Controller\ResultFactory;
use Magento\Framework\View\Result\PageFactory;
use Psr\Log\LoggerInterface;
use Stripe\Account;

class Verify extends Action
{
    /**
     * Construct function
     *
     * @param \Magento\Framework\App\Action\Context $context
     * @param \Magento\Framework\View\Result\PageFactory $resultPageFactory
     * @param \Comave\SellerPayouts\Helper\Data $_stripeHelper
     * @param \Magento\Customer\Model\Session $_customerSession
     */
    public function __construct(
        Context $context,
        protected PageFactory $resultPageFactory,
        protected StripeHelper $_stripeHelper,
        protected CustomerSession $_customerSession,
        private readonly LoggerInterface $logger
    ) {
        parent::__construct($context);
    }

    /**
     * Execute function
     *
     * @return \Magento\Framework\Controller\Result\Redirect
     */
    public function execute(): \Magento\Framework\Controller\Result\Redirect
    {
        $data = $this->getRequest()->getPostValue();
        $email = $data['stripe_account_email'] ?? '';
        $country = $data['seller_stripe_country'] ?? '';
        $name = $data['stripe_account_name'] ?? '';

        // Log the received data for debugging
        $this->logger->info('Stripe Account Creation Request', [
            'email' => $email,
            'country' => $country,
            'name' => $name,
            'all_post_data' => $data
        ]);

        $resultRedirect = $this->resultFactory->create(ResultFactory::TYPE_REDIRECT);

        // Validate required fields
        if (empty($email) || empty($country) || empty($name)) {
            $this->logger->info('Missing required fields for Stripe account creation', [
                'email_empty' => empty($email),
                'country_empty' => empty($country),
                'name_empty' => empty($name)
            ]);
            $this->messageManager->addErrorMessage(__('Please fill in all required fields.'));
            return $resultRedirect->setPath('seller_payouts/seller/payouts');
        }

        // Log the input parameters being passed
        $this->logger->info('About to call createSellerStripeAccount', [
            'helper_class' => get_class($this->_stripeHelper),
            'method_exists' => method_exists($this->_stripeHelper, 'createSellerStripeAccount'),
            'name' => $name,
            'email' => $email,
            'country' => $country,
            'email_valid' => filter_var($email, FILTER_VALIDATE_EMAIL) !== false,
            'country_length' => strlen($country),
            'name_length' => strlen($name)
        ]);

        $verifiedAccount = $this->_stripeHelper->createSellerStripeAccount((string) $email, (string) $country);

        $this->logger->info('createSellerStripeAccount returned', [
            'result_type' => gettype($verifiedAccount),
            'result_class' => is_object($verifiedAccount) ? get_class($verifiedAccount) : null,
            'has_id_property' => is_object($verifiedAccount) ? isset($verifiedAccount->id) : null,
            'account_id' => is_object($verifiedAccount) && isset($verifiedAccount->id) ? $verifiedAccount->id : null
        ]);

        if (!$verifiedAccount || (!is_object($verifiedAccount) && !isset($verifiedAccount['id'])) || (is_object($verifiedAccount) && !isset($verifiedAccount->id))) {
            $this->logger->info('Stripe Account Creation Failed', [
                'email' => $email,
                'country' => $country,
                'name' => $name,
                'account_result_type' => gettype($verifiedAccount),
                'account_result' => is_object($verifiedAccount) ? get_class($verifiedAccount) : $verifiedAccount
            ]);
            return $this->handleAccountCreationFailure($resultRedirect);
        }

        $this->logger->info('Stripe Account Created Successfully', [
            'account_id' => $verifiedAccount['id'] ?? 'unknown',
            'email' => $email,
            'country' => $country
        ]);

        return $this->handleSuccessfulAccountCreation($verifiedAccount, $name, $email, $country, $resultRedirect);
    }

    /**
     * Handle Successful Account Creation function
     *
     * @param \Stripe\Account|string $verifiedAccount
     * @param string $name
     * @param string $email
     * @param string $country
     * @param \Magento\Framework\Controller\Result\Redirect $resultRedirect
     * @return \Magento\Framework\Controller\Result\Redirect
     */
    private function handleSuccessfulAccountCreation(
        Account|string $verifiedAccount,
        string $name,
        string $email,
        string $country,
        Redirect $resultRedirect
    ): Redirect {
        try {
            $customer = $this->_customerSession->getCustomer();
            $this->processCustomerAccount($customer, $verifiedAccount, $name, $email, $country);
            $accountLink = $this->createAccountLink($verifiedAccount);

            if (!$accountLink) {
                return $this->handleAccountLinkError($resultRedirect);
            }

            $this->handleSuccessMessage($accountLink);
            $resultRedirect->setPath('seller_payouts/seller/Payouts');
        } catch (\Exception $e) {
            $this->handleError($e, $resultRedirect);
        }

        return $resultRedirect;
    }

    /**
     * Process the customer account by deleting the existing Stripe account and updating customer data.
     *
     * @param \Magento\Customer\Model\Customer $customer
     * @param \Stripe\Account|string $verifiedAccount
     * @param string $name
     * @param string $email
     * @param string $country
     * @return void
     */
    private function processCustomerAccount(
        Customer $customer,
        Account|string $verifiedAccount,
        string $name,
        string $email,
        string $country
    ): void {
        $this->deleteExistingStripeAccount($customer);
        $this->updateCustomerData($customer, $verifiedAccount['id'], $name, $email, $country);
    }

    /**
     * Create a Stripe account link for the verified account.
     *
     * @param \Stripe\Account|string $verifiedAccount
     * @return string|null
     */
    private function createAccountLink(Account|string $verifiedAccount): ?string
    {
        return $this->_stripeHelper->createStripeAccountLink((string) $verifiedAccount['id']);
    }

    /**
     * Handle the error case when the account link cannot be created.
     *
     * @param \Magento\Framework\Controller\Result\Redirect $resultRedirect
     * @return \Magento\Framework\Controller\Result\Redirect
     */
    private function handleAccountLinkError(Redirect $resultRedirect): Redirect
    {
        $this->messageManager->addErrorMessage('There was a problem receiving the Stripe Verify Url.');

        return $resultRedirect->setPath('*/*/payouts');
    }

    /**
     * Display a success message with the account link for the user.
     *
     * @param string $accountLink
     * @return void
     */
    private function handleSuccessMessage(string $accountLink): void
    {
        $this->messageManager->addSuccess(
            __(
                'Congratulations! Your account is created on the ComAve Stripe platform. ' .
                'Please verify your account <a href="%1">here</a>.',
                $accountLink
            )
        );
    }

    /**
     * Handle errors that occur during the account creation process.
     *
     * @param \Exception $e
     * @param \Magento\Framework\Controller\Result\Redirect $resultRedirect
     * @return void
     */
    private function handleError(\Exception $e, Redirect $resultRedirect): void
    {
        $this->messageManager->addErrorMessage(
            __('There was a problem connecting your Stripe account: %1', $e->getMessage())
        );
        $resultRedirect->setPath('*/*/payouts');
    }

    /**
     * Handle Account Creation Failure function
     *
     * @param \Magento\Framework\Controller\Result\Redirect $resultRedirect
     * @return \Magento\Framework\Controller\Result\Redirect
     */
    private function handleAccountCreationFailure(Redirect $resultRedirect): Redirect
    {
        // Get the last error from session or logs for more specific information
        $this->messageManager->addErrorMessage(
            __('Failed to create Stripe account. Please check the logs for detailed error information. Common issues: unsupported country, invalid email format, or Stripe API configuration problems.')
        );
        $resultRedirect->setPath('seller_payouts/seller/payouts');

        return $resultRedirect;
    }

    /**
     * Delete Existing Stripe Account function
     *
     * @param \Magento\Customer\Model\Customer $customer
     * @return void
     */
    private function deleteExistingStripeAccount(Customer $customer): void
    {
        $stripeClientId = $customer->getData('stripe_client_id');

        if (!empty($stripeClientId)) {
            $this->_stripeHelper->deleteSellerStripeAccount((string) $stripeClientId);
        }
    }

    /**
     * Update Customer Data function
     *
     * @param \Magento\Customer\Model\Customer $customer
     * @param string $stripeClientId
     * @param string $name
     * @param string $email
     * @param string $country
     * @return void
     */
    private function updateCustomerData(
        Customer $customer,
        string $stripeClientId,
        string $name,
        string $email,
        string $country
    ): void {
        $customer->setData('stripe_client_id', $stripeClientId);
        $customer->setData('stripe_account_name', $name);
        $customer->setData('stripe_account_email', $email);
        $customer->setData('seller_stripe_country', $country);
        $customer->save();
    }
}
