<?php
/**
 * Test script for Stripe account creation debugging
 * Run this from the Magento root directory: php test_stripe_account_creation.php
 */

use Magento\Framework\App\Bootstrap;

require __DIR__ . '/app/bootstrap.php';

$bootstrap = Bootstrap::create(BP, $_SERVER);
$objectManager = $bootstrap->getObjectManager();

// Get the Stripe helper
$stripeHelper = $objectManager->get(\Comave\SellerPayouts\Helper\Data::class);

// Test parameters - replace with actual test values
$testEmail = '<EMAIL>';  // Replace with a valid test email
$testCountry = 'US';              // Replace with a valid country code

echo "=== Stripe Account Creation Test ===\n";
echo "Email: $testEmail\n";
echo "Country: $testCountry\n";
echo "=====================================\n\n";

echo "Starting Stripe account creation test...\n";
echo "Check var/log/debug.log for detailed logs from the ComaveLogger.\n\n";

try {
    $result = $stripeHelper->createSellerStripeAccount($testEmail, $testCountry);
    
    if ($result) {
        echo "SUCCESS: Stripe account created!\n";
        if (is_object($result)) {
            echo "Account ID: " . ($result->id ?? 'N/A') . "\n";
            echo "Account Type: " . ($result->type ?? 'N/A') . "\n";
            echo "Country: " . ($result->country ?? 'N/A') . "\n";
            echo "Email: " . ($result->email ?? 'N/A') . "\n";
        } else {
            echo "Result: " . var_export($result, true) . "\n";
        }
    } else {
        echo "FAILED: Stripe account creation returned false\n";
    }
} catch (\Exception $e) {
    echo "EXCEPTION: " . $e->getMessage() . "\n";
    echo "Class: " . get_class($e) . "\n";
    echo "File: " . $e->getFile() . " Line: " . $e->getLine() . "\n";
}

echo "\n=== Test Complete ===\n";
echo "Check the following log files for detailed information:\n";
echo "- var/log/debug.log (ComaveLogger entries for SellerPayouts)\n";
echo "- var/log/exception.log (if any exceptions occurred)\n";
